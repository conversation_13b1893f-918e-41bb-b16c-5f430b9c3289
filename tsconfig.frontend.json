{"compilerOptions": {"target": "ES2020", "module": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "outDir": "./public/js/dist", "rootDir": "./public/js/src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "sourceMap": true, "declaration": false, "removeComments": false, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true}, "include": ["public/js/src/**/*"], "exclude": ["node_modules", "dist", "src"]}