const simpleGit = require('simple-git');
const path = require('path');

class GitService {
    constructor(repoPath = process.cwd()) {
        this.repoPath = repoPath;
        this.git = simpleGit(repoPath);
    }

    async getCommits(limit = 50) {
        try {
            const log = await this.git.log({
                maxCount: limit,
                format: {
                    hash: '%H',
                    date: '%ai',
                    message: '%s',
                    author_name: '%an',
                    author_email: '%ae',
                    refs: '%D'
                }
            });

            return log.all.map(commit => ({
                hash: commit.hash,
                shortHash: commit.hash.substring(0, 7),
                message: commit.message,
                author: commit.author_name,
                email: commit.author_email,
                date: commit.date,
                refs: commit.refs || ''
            }));
        } catch (error) {
            throw new Error(`Failed to get commits: ${error.message}`);
        }
    }

    async getBranches() {
        try {
            const branches = await this.git.branch(['-a']);
            return {
                current: branches.current,
                all: branches.all,
                local: Object.keys(branches.branches).filter(name => !name.startsWith('remotes/')),
                remote: Object.keys(branches.branches).filter(name => name.startsWith('remotes/'))
            };
        } catch (error) {
            throw new Error(`Failed to get branches: ${error.message}`);
        }
    }

    async getCommitDetails(hash) {
        try {
            const show = await this.git.show([hash, '--stat', '--format=fuller']);
            const diff = await this.git.diff([`${hash}^`, hash, '--name-status']);
            
            return {
                hash,
                details: show,
                files: this.parseFileDiff(diff)
            };
        } catch (error) {
            throw new Error(`Failed to get commit details: ${error.message}`);
        }
    }

    parseFileDiff(diff) {
        const lines = diff.split('\n').filter(line => line.trim());
        return lines.map(line => {
            const [status, ...fileParts] = line.split('\t');
            const file = fileParts.join('\t');
            return {
                status: status.charAt(0),
                file: file,
                statusText: this.getStatusText(status.charAt(0))
            };
        });
    }

    getStatusText(status) {
        const statusMap = {
            'A': 'Added',
            'M': 'Modified',
            'D': 'Deleted',
            'R': 'Renamed',
            'C': 'Copied',
            'U': 'Unmerged'
        };
        return statusMap[status] || 'Unknown';
    }

    async getRepoInfo() {
        try {
            const status = await this.git.status();
            const remotes = await this.git.getRemotes(true);
            
            return {
                path: this.repoPath,
                current: status.current,
                ahead: status.ahead,
                behind: status.behind,
                staged: status.staged.length,
                modified: status.modified.length,
                untracked: status.not_added.length,
                remotes: remotes
            };
        } catch (error) {
            throw new Error(`Failed to get repo info: ${error.message}`);
        }
    }

    async getCommitGraph(limit = 50) {
        try {
            const log = await this.git.raw([
                'log',
                '--graph',
                '--pretty=format:%H|%P|%s|%an|%ad|%D',
                '--date=iso',
                `--max-count=${limit}`,
                '--all'
            ]);

            const lines = log.split('\n');
            const commits = [];

            lines.forEach((line, index) => {
                if (line.includes('|')) {
                    const graphPart = line.substring(0, line.indexOf(' '));
                    const dataPart = line.substring(line.indexOf(' ') + 1);
                    const [hash, parents, message, author, date, refs] = dataPart.split('|');

                    commits.push({
                        hash,
                        shortHash: hash.substring(0, 7),
                        parents: parents ? parents.split(' ') : [],
                        message,
                        author,
                        date,
                        refs: refs || '',
                        graph: graphPart,
                        level: this.calculateLevel(graphPart)
                    });
                }
            });

            return commits;
        } catch (error) {
            throw new Error(`Failed to get commit graph: ${error.message}`);
        }
    }

    calculateLevel(graphPart) {
        // Simple level calculation based on graph characters
        let level = 0;
        for (let char of graphPart) {
            if (char === '|' || char === '*' || char === '\\' || char === '/') {
                level++;
            }
        }
        return Math.max(0, level - 1);
    }
}

module.exports = GitService;
