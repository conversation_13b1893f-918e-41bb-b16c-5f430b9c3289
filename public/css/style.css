/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #0d1117;
    color: #e6edf3;
    line-height: 1.6;
    overflow-x: hidden;
}

/* Layout */
#app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    background: #161b22;
    border-bottom: 1px solid #30363d;
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo i {
    font-size: 2rem;
    color: #f78166;
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #e6edf3;
}

.repo-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    flex: 1;
    max-width: 600px;
}

.repo-path {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #7d8590;
}

.repo-status {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.branch, .changes {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    padding: 0.25rem 0.75rem;
    border-radius: 6px;
    background: #21262d;
}

.branch i {
    color: #7c3aed;
}

.changes i {
    color: #f85149;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

/* Buttons */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
}

.btn-secondary {
    background: #21262d;
    color: #e6edf3;
    border: 1px solid #30363d;
}

.btn-secondary:hover {
    background: #30363d;
    border-color: #484f58;
}

/* Main Content */
.main-content {
    display: flex;
    flex: 1;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

/* Sidebar */
.sidebar {
    width: 300px;
    background: #0d1117;
    border-right: 1px solid #21262d;
    padding: 1.5rem;
    overflow-y: auto;
    max-height: calc(100vh - 80px);
}

.sidebar-section {
    margin-bottom: 2rem;
}

.sidebar-section h3 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #e6edf3;
}

.branches-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.branch-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    border-radius: 6px;
    cursor: pointer;
    transition: background 0.2s ease;
    font-size: 0.875rem;
}

.branch-item:hover {
    background: #21262d;
}

.branch-item.current {
    background: #1f2937;
    color: #7c3aed;
    font-weight: 500;
}

.branch-item i {
    font-size: 0.75rem;
    opacity: 0.7;
}

.repo-stats {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
}

.stat .label {
    color: #7d8590;
}

.stat .value {
    font-weight: 500;
    color: #e6edf3;
}

/* Content Area */
.content-area {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
    max-height: calc(100vh - 80px);
}

/* Sections */
.commit-graph-section,
.commit-list-section {
    background: #161b22;
    border: 1px solid #21262d;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #21262d;
    background: #0d1117;
}

.section-header h2 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.1rem;
    font-weight: 600;
}

.graph-controls {
    display: flex;
    gap: 1rem;
}

.select {
    background: #21262d;
    color: #e6edf3;
    border: 1px solid #30363d;
    border-radius: 6px;
    padding: 0.5rem;
    font-size: 0.875rem;
}

/* Commit Graph */
.commit-graph {
    position: relative;
    min-height: 400px;
    padding: 1rem;
}

#graphCanvas {
    width: 100%;
    height: 400px;
    background: #0d1117;
    border-radius: 6px;
}

.graph-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #7d8590;
    font-size: 0.875rem;
}

/* Commit List */
.commit-list {
    max-height: 600px;
    overflow-y: auto;
}

.commit-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #21262d;
    cursor: pointer;
    transition: background 0.2s ease;
}

.commit-item:hover {
    background: #0d1117;
}

.commit-item:last-child {
    border-bottom: none;
}

.commit-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(45deg, #7c3aed, #f59e0b);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.commit-info {
    flex: 1;
    min-width: 0;
}

.commit-message {
    font-weight: 500;
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.commit-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 0.75rem;
    color: #7d8590;
}

.commit-hash {
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    background: #21262d;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
}

.commit-refs {
    display: flex;
    gap: 0.5rem;
}

.ref-tag {
    background: #1f2937;
    color: #7c3aed;
    padding: 0.125rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 500;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    backdrop-filter: blur(4px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: #161b22;
    border: 1px solid #30363d;
    border-radius: 8px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #21262d;
    background: #0d1117;
}

.modal-header h3 {
    font-size: 1.1rem;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    color: #7d8590;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: color 0.2s ease;
}

.modal-close:hover {
    color: #e6edf3;
}

.modal-body {
    padding: 1.5rem;
    overflow-y: auto;
    flex: 1;
}

/* Toast */
.toast {
    position: fixed;
    top: 1rem;
    right: 1rem;
    background: #da3633;
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 6px;
    display: none;
    align-items: center;
    gap: 0.75rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 1100;
}

.toast.show {
    display: flex;
}

.toast-close {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    opacity: 0.8;
    transition: opacity 0.2s ease;
}

.toast-close:hover {
    opacity: 1;
}

/* Loading States */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 2rem;
    color: #7d8590;
    font-size: 0.875rem;
}

.loading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .main-content {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        max-height: none;
        border-right: none;
        border-bottom: 1px solid #21262d;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .repo-info {
        max-width: none;
    }
}

@media (max-width: 768px) {
    .header-content {
        padding: 0 1rem;
    }
    
    .content-area {
        padding: 1rem;
    }
    
    .sidebar {
        padding: 1rem;
    }
    
    .modal-content {
        width: 95%;
        margin: 1rem;
    }
}
