"use strict";
class GitViewer {
    constructor() {
        this.commits = [];
        this.repoInfo = null;
        this.branches = null;
        this.init();
    }
    async init() {
        this.setupEventListeners();
        await this.loadData();
    }
    setupEventListeners() {
        // Refresh button
        const refreshBtn = document.getElementById('refreshBtn');
        refreshBtn?.addEventListener('click', () => this.loadData());
        // Commit limit selector
        const commitLimit = document.getElementById('commitLimit');
        commitLimit?.addEventListener('change', () => this.loadCommits());
        // Modal close
        const modalClose = document.getElementById('modalClose');
        const modal = document.getElementById('commitModal');
        modalClose?.addEventListener('click', () => this.closeModal());
        modal?.addEventListener('click', (e) => {
            if (e.target === modal)
                this.closeModal();
        });
        // Error toast close
        const errorToastClose = document.getElementById('errorToastClose');
        errorToastClose?.addEventListener('click', () => this.hideError());
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape')
                this.closeModal();
            if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
                e.preventDefault();
                this.loadData();
            }
        });
    }
    async loadData() {
        try {
            await Promise.all([
                this.loadRepoInfo(),
                this.loadBranches(),
                this.loadCommits()
            ]);
        }
        catch (error) {
            this.showError('Failed to load repository data');
            console.error('Error loading data:', error);
        }
    }
    async loadRepoInfo() {
        try {
            const response = await fetch('/api/git/info');
            const result = await response.json();
            if (result.success && result.data) {
                this.repoInfo = result.data;
                this.updateRepoInfoUI();
            }
            else {
                throw new Error(result.error || 'Failed to load repository info');
            }
        }
        catch (error) {
            console.error('Error loading repo info:', error);
            throw error;
        }
    }
    async loadBranches() {
        try {
            const response = await fetch('/api/git/branches');
            const result = await response.json();
            if (result.success && result.data) {
                this.branches = result.data;
                this.updateBranchesUI();
            }
            else {
                throw new Error(result.error || 'Failed to load branches');
            }
        }
        catch (error) {
            console.error('Error loading branches:', error);
            throw error;
        }
    }
    async loadCommits() {
        try {
            const commitLimit = document.getElementById('commitLimit');
            const limit = parseInt(commitLimit?.value || '50');
            // Show loading state
            const commitList = document.getElementById('commitList');
            if (commitList) {
                commitList.innerHTML = `
                    <div class="loading">
                        <i class="fas fa-spinner fa-spin"></i>
                        Loading commits...
                    </div>
                `;
            }
            const response = await fetch(`/api/git/commits/graph?limit=${limit}`);
            const result = await response.json();
            if (result.success && result.data) {
                this.commits = result.data;
                this.updateCommitsUI();
                this.drawCommitGraph();
            }
            else {
                throw new Error(result.error || 'Failed to load commits');
            }
        }
        catch (error) {
            console.error('Error loading commits:', error);
            const commitList = document.getElementById('commitList');
            if (commitList) {
                commitList.innerHTML = `
                    <div class="loading">
                        <i class="fas fa-exclamation-triangle"></i>
                        Failed to load commits
                    </div>
                `;
            }
            throw error;
        }
    }
    updateRepoInfoUI() {
        if (!this.repoInfo)
            return;
        // Update repo path
        const repoPath = document.getElementById('repoPath');
        if (repoPath) {
            const pathSpan = repoPath.querySelector('span');
            if (pathSpan) {
                pathSpan.textContent = this.repoInfo.path;
            }
        }
        // Update current branch
        const currentBranch = document.getElementById('currentBranch');
        if (currentBranch) {
            const branchSpan = currentBranch.querySelector('span');
            if (branchSpan) {
                branchSpan.textContent = this.repoInfo.current;
            }
        }
        // Update changes count
        const repoChanges = document.getElementById('repoChanges');
        if (repoChanges) {
            const changesSpan = repoChanges.querySelector('span');
            const totalChanges = this.repoInfo.staged + this.repoInfo.modified + this.repoInfo.untracked;
            if (changesSpan) {
                changesSpan.textContent = `${totalChanges} changes`;
            }
        }
        // Update stats
        this.updateElement('aheadCount', this.repoInfo.ahead.toString());
        this.updateElement('behindCount', this.repoInfo.behind.toString());
        this.updateElement('stagedCount', this.repoInfo.staged.toString());
        this.updateElement('modifiedCount', this.repoInfo.modified.toString());
    }
    updateBranchesUI() {
        if (!this.branches)
            return;
        const branchesList = document.getElementById('branchesList');
        if (!branchesList)
            return;
        const localBranches = this.branches.local.map(branch => `
            <div class="branch-item ${branch === this.branches.current ? 'current' : ''}" data-branch="${branch}">
                <i class="fas fa-code-branch"></i>
                <span>${branch}</span>
                ${branch === this.branches.current ? '<i class="fas fa-check"></i>' : ''}
            </div>
        `).join('');
        branchesList.innerHTML = localBranches;
        // Add click handlers for branch items
        branchesList.querySelectorAll('.branch-item').forEach(item => {
            item.addEventListener('click', () => {
                const branchName = item.getAttribute('data-branch');
                if (branchName) {
                    this.showInfo(`Branch switching not implemented yet: ${branchName}`);
                }
            });
        });
    }
    updateCommitsUI() {
        const commitList = document.getElementById('commitList');
        if (!commitList)
            return;
        if (this.commits.length === 0) {
            commitList.innerHTML = `
                <div class="loading">
                    <i class="fas fa-info-circle"></i>
                    No commits found
                </div>
            `;
            return;
        }
        const commitsHtml = this.commits.map(commit => {
            const date = new Date(commit.date);
            const formattedDate = date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
            const authorInitials = commit.author.split(' ').map(n => n[0]).join('').toUpperCase();
            const refs = commit.refs ? commit.refs.split(', ').map(ref => {
                const cleanRef = ref.replace(/origin\/|HEAD -> /, '');
                return `<span class="ref-tag">${cleanRef}</span>`;
            }).join('') : '';
            return `
                <div class="commit-item" data-hash="${commit.hash}">
                    <div class="commit-avatar">${authorInitials}</div>
                    <div class="commit-info">
                        <div class="commit-message">${this.escapeHtml(commit.message)}</div>
                        <div class="commit-meta">
                            <span>${commit.author}</span>
                            <span>${formattedDate}</span>
                            <span class="commit-hash">${commit.shortHash}</span>
                        </div>
                        ${refs ? `<div class="commit-refs">${refs}</div>` : ''}
                    </div>
                </div>
            `;
        }).join('');
        commitList.innerHTML = commitsHtml;
        // Add click handlers for commit items
        commitList.querySelectorAll('.commit-item').forEach(item => {
            item.addEventListener('click', () => {
                const hash = item.getAttribute('data-hash');
                if (hash) {
                    this.showCommitDetails(hash);
                }
            });
        });
    }
    drawCommitGraph() {
        const canvas = document.getElementById('graphCanvas');
        const loading = document.querySelector('.graph-loading');
        if (!canvas || !this.commits.length)
            return;
        // Hide loading
        if (loading)
            loading.style.display = 'none';
        const ctx = canvas.getContext('2d');
        if (!ctx)
            return;
        // Set canvas size
        const rect = canvas.getBoundingClientRect();
        canvas.width = rect.width * window.devicePixelRatio;
        canvas.height = rect.height * window.devicePixelRatio;
        ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
        // Clear canvas
        ctx.clearRect(0, 0, rect.width, rect.height);
        // Graph settings
        const nodeRadius = 4;
        const rowHeight = 25;
        const colWidth = 20;
        const startX = 30;
        const startY = 20;
        // Colors for different branches
        const colors = ['#7c3aed', '#f59e0b', '#10b981', '#ef4444', '#3b82f6', '#8b5cf6'];
        // Draw commits
        this.commits.forEach((commit, index) => {
            const x = startX + (commit.level || 0) * colWidth;
            const y = startY + index * rowHeight;
            const color = colors[(commit.level || 0) % colors.length];
            // Draw connection lines to parents
            if (commit.parents && commit.parents.length > 0) {
                commit.parents.forEach(parentHash => {
                    const parentIndex = this.commits.findIndex(c => c.hash === parentHash);
                    if (parentIndex !== -1 && parentIndex > index) {
                        const parentCommit = this.commits[parentIndex];
                        const parentX = startX + (parentCommit.level || 0) * colWidth;
                        const parentY = startY + parentIndex * rowHeight;
                        ctx.strokeStyle = color;
                        ctx.lineWidth = 2;
                        ctx.beginPath();
                        ctx.moveTo(x, y);
                        ctx.lineTo(parentX, parentY);
                        ctx.stroke();
                    }
                });
            }
            // Draw commit node
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.arc(x, y, nodeRadius, 0, 2 * Math.PI);
            ctx.fill();
            // Draw commit message
            ctx.fillStyle = '#e6edf3';
            ctx.font = '12px Inter, sans-serif';
            ctx.fillText(commit.message.length > 50 ? commit.message.substring(0, 50) + '...' : commit.message, x + 15, y + 4);
            // Draw commit hash
            ctx.fillStyle = '#7d8590';
            ctx.font = '10px SF Mono, monospace';
            ctx.fillText(commit.shortHash, x + 15, y - 8);
        });
    }
    async showCommitDetails(hash) {
        const modal = document.getElementById('commitModal');
        const modalTitle = document.getElementById('modalTitle');
        const modalBody = document.getElementById('modalBody');
        if (!modal || !modalTitle || !modalBody)
            return;
        // Show modal with loading state
        modal.classList.add('show');
        modalTitle.textContent = `Commit ${hash.substring(0, 7)}`;
        modalBody.innerHTML = `
            <div class="loading">
                <i class="fas fa-spinner fa-spin"></i>
                Loading commit details...
            </div>
        `;
        try {
            const response = await fetch(`/api/git/commits/${hash}`);
            const result = await response.json();
            if (result.success && result.data) {
                const { details, files } = result.data;
                const filesHtml = files.map((file) => `
                    <div class="file-change">
                        <span class="file-status status-${file.status.toLowerCase()}">${file.statusText}</span>
                        <span class="file-name">${file.file}</span>
                    </div>
                `).join('');
                modalBody.innerHTML = `
                    <div class="commit-details">
                        <h4>Changed Files</h4>
                        <div class="files-list">
                            ${filesHtml || '<p>No files changed</p>'}
                        </div>
                        <h4>Commit Details</h4>
                        <pre class="commit-diff">${this.escapeHtml(details)}</pre>
                    </div>
                `;
            }
            else {
                throw new Error(result.error || 'Failed to load commit details');
            }
        }
        catch (error) {
            modalBody.innerHTML = `
                <div class="error">
                    <i class="fas fa-exclamation-triangle"></i>
                    Failed to load commit details: ${error.message}
                </div>
            `;
        }
    }
    closeModal() {
        const modal = document.getElementById('commitModal');
        modal?.classList.remove('show');
    }
    showError(message) {
        const errorToast = document.getElementById('errorToast');
        const errorMessage = document.getElementById('errorMessage');
        if (errorToast && errorMessage) {
            errorMessage.textContent = message;
            errorToast.classList.add('show');
            // Auto hide after 5 seconds
            setTimeout(() => this.hideError(), 5000);
        }
    }
    hideError() {
        const errorToast = document.getElementById('errorToast');
        errorToast?.classList.remove('show');
    }
    showInfo(message) {
        // Simple alert for now - could be replaced with a proper info toast
        alert(message);
    }
    updateElement(id, text) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = text;
        }
    }
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}
// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new GitViewer();
});
//# sourceMappingURL=app.js.map