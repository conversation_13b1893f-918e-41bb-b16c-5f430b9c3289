<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GitViewer - Visual Git Repository Explorer</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div id="app">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fab fa-git-alt"></i>
                    <h1>GitViewer</h1>
                </div>
                <div class="repo-info">
                    <div class="repo-path" id="repoPath">
                        <i class="fas fa-folder"></i>
                        <span>Loading repository...</span>
                    </div>
                    <div class="repo-status" id="repoStatus">
                        <span class="branch" id="currentBranch">
                            <i class="fas fa-code-branch"></i>
                            <span>main</span>
                        </span>
                        <span class="changes" id="repoChanges">
                            <i class="fas fa-circle-dot"></i>
                            <span>0 changes</span>
                        </span>
                    </div>
                </div>
                <div class="header-actions">
                    <button class="btn btn-secondary" id="refreshBtn">
                        <i class="fas fa-sync-alt"></i>
                        Refresh
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Sidebar -->
            <aside class="sidebar">
                <div class="sidebar-section">
                    <h3>
                        <i class="fas fa-code-branch"></i>
                        Branches
                    </h3>
                    <div class="branches-list" id="branchesList">
                        <div class="loading">Loading branches...</div>
                    </div>
                </div>
                
                <div class="sidebar-section">
                    <h3>
                        <i class="fas fa-info-circle"></i>
                        Repository Info
                    </h3>
                    <div class="repo-stats" id="repoStats">
                        <div class="stat">
                            <span class="label">Ahead:</span>
                            <span class="value" id="aheadCount">0</span>
                        </div>
                        <div class="stat">
                            <span class="label">Behind:</span>
                            <span class="value" id="behindCount">0</span>
                        </div>
                        <div class="stat">
                            <span class="label">Staged:</span>
                            <span class="value" id="stagedCount">0</span>
                        </div>
                        <div class="stat">
                            <span class="label">Modified:</span>
                            <span class="value" id="modifiedCount">0</span>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- Content Area -->
            <div class="content-area">
                <!-- Commit Graph -->
                <section class="commit-graph-section">
                    <div class="section-header">
                        <h2>
                            <i class="fas fa-project-diagram"></i>
                            Commit Graph
                        </h2>
                        <div class="graph-controls">
                            <select id="commitLimit" class="select">
                                <option value="25">25 commits</option>
                                <option value="50" selected>50 commits</option>
                                <option value="100">100 commits</option>
                                <option value="200">200 commits</option>
                            </select>
                        </div>
                    </div>
                    <div class="commit-graph" id="commitGraph">
                        <canvas id="graphCanvas"></canvas>
                        <div class="graph-loading">
                            <i class="fas fa-spinner fa-spin"></i>
                            Loading commit graph...
                        </div>
                    </div>
                </section>

                <!-- Commit List -->
                <section class="commit-list-section">
                    <div class="section-header">
                        <h2>
                            <i class="fas fa-list"></i>
                            Recent Commits
                        </h2>
                    </div>
                    <div class="commit-list" id="commitList">
                        <div class="loading">
                            <i class="fas fa-spinner fa-spin"></i>
                            Loading commits...
                        </div>
                    </div>
                </section>
            </div>
        </main>

        <!-- Commit Details Modal -->
        <div class="modal" id="commitModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="modalTitle">Commit Details</h3>
                    <button class="modal-close" id="modalClose">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body" id="modalBody">
                    <div class="loading">
                        <i class="fas fa-spinner fa-spin"></i>
                        Loading commit details...
                    </div>
                </div>
            </div>
        </div>

        <!-- Error Toast -->
        <div class="toast toast-error" id="errorToast">
            <i class="fas fa-exclamation-circle"></i>
            <span id="errorMessage">An error occurred</span>
            <button class="toast-close" id="errorToastClose">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>

    <!-- Scripts -->
    <script type="module" src="js/dist/app.js"></script>
</body>
</html>
