# GitViewer 🚀

A modern, visual Git repository viewer built with TypeScript and Node.js. Inspired by GitKraken, GitViewer provides an intuitive interface to explore your Git repositories with beautiful commit graphs and detailed commit information.

## ✨ Features

- **Visual Commit Graph**: Interactive commit graph similar to GitKraken
- **Commit History**: Detailed list of commits with author, date, and message
- **Branch Visualization**: View all local and remote branches
- **Commit Details**: Click on any commit to see detailed changes
- **Repository Info**: Real-time repository status and statistics
- **Modern UI**: Dark theme with responsive design
- **TypeScript**: Fully typed codebase for better development experience

## 🛠️ Tech Stack

- **Backend**: Node.js + Express + TypeScript
- **Frontend**: TypeScript + HTML5 Canvas + CSS3
- **Git Integration**: simple-git library
- **Build Tools**: TypeScript compiler, nodemon for development

## 📦 Installation

1. **Clone or navigate to your project directory**:
   ```bash
   cd /path/to/your/git/repository
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Build the project**:
   ```bash
   npm run build
   npm run build:frontend
   ```

## 🚀 Usage

### Development Mode
```bash
# Start backend in development mode
npm run dev

# In another terminal, watch frontend changes
npm run dev:frontend
```

### Production Mode
```bash
# Build everything
npm run build
npm run build:frontend

# Start the server
npm start
```

The application will be available at `http://localhost:3000`

## 📁 Project Structure

```
GitViewer/
├── src/                    # Backend TypeScript source
│   ├── server.ts          # Express server
│   ├── routes/            # API routes
│   ├── services/          # Business logic
│   └── types/             # TypeScript interfaces
├── public/                # Frontend assets
│   ├── index.html         # Main HTML file
│   ├── css/               # Stylesheets
│   └── js/                # Frontend TypeScript
├── dist/                  # Compiled backend code
├── package.json           # Dependencies and scripts
├── tsconfig.json          # Backend TypeScript config
└── tsconfig.frontend.json # Frontend TypeScript config
```

## 🎯 API Endpoints

- `GET /api/git/info` - Repository information
- `GET /api/git/commits` - List of commits
- `GET /api/git/commits/graph` - Commit graph data
- `GET /api/git/commits/:hash` - Detailed commit information
- `GET /api/git/branches` - Branch information

## 🎨 Features Overview

### Commit Graph
- Visual representation of commit history
- Branch merges and splits
- Color-coded branches
- Interactive commit nodes

### Commit List
- Chronological commit history
- Author avatars and information
- Commit messages and metadata
- Branch and tag references

### Repository Dashboard
- Current branch status
- Ahead/behind commit counts
- Staged, modified, and untracked file counts
- Remote repository information

### Commit Details Modal
- File changes with status indicators
- Full commit diff information
- Commit metadata and statistics

## 🔧 Configuration

The application automatically detects Git repositories in the current working directory. Make sure to run GitViewer from within a Git repository.

### Environment Variables
- `PORT` - Server port (default: 3000)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📝 Scripts

- `npm run build` - Build backend TypeScript
- `npm run build:frontend` - Build frontend TypeScript
- `npm start` - Start production server
- `npm run dev` - Start development server with hot reload
- `npm run dev:frontend` - Watch frontend changes
- `npm run clean` - Clean build directories

## 🐛 Troubleshooting

### Common Issues

1. **"Not a valid Git repository"**
   - Make sure you're running GitViewer from within a Git repository
   - Check that `.git` directory exists

2. **TypeScript compilation errors**
   - Run `npm run clean` and rebuild
   - Check TypeScript version compatibility

3. **Port already in use**
   - Set a different port: `PORT=3001 npm start`
   - Kill existing processes on port 3000

## 📄 License

MIT License - see LICENSE file for details

## 🙏 Acknowledgments

- Inspired by GitKraken's beautiful Git visualization
- Built with love for the developer community
- Thanks to all contributors and users

---

**Happy Git exploring! 🎉**
