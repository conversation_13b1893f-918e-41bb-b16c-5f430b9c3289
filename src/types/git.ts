export interface GitCommit {
  hash: string;
  shortHash: string;
  message: string;
  author: string;
  email: string;
  date: string;
  refs: string;
  parents?: string[];
  graph?: string;
  level?: number;
}

export interface GitBranch {
  name: string;
  current: boolean;
  remote: boolean;
  commit: string;
}

export interface GitBranches {
  current: string;
  all: string[];
  local: string[];
  remote: string[];
}

export interface GitFileChange {
  status: string;
  file: string;
  statusText: string;
}

export interface GitCommitDetails {
  hash: string;
  details: string;
  files: GitFileChange[];
}

export interface GitRepoInfo {
  path: string;
  current: string;
  ahead: number;
  behind: number;
  staged: number;
  modified: number;
  untracked: number;
  remotes: GitRemote[];
}

export interface GitRemote {
  name: string;
  refs: {
    fetch: string;
    push: string;
  };
}

export interface GitLogOptions {
  maxCount?: number;
  from?: string;
  to?: string;
  format?: Record<string, string>;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface CommitGraphNode {
  commit: GitCommit;
  x: number;
  y: number;
  color: string;
  connections: Connection[];
}

export interface Connection {
  from: { x: number; y: number };
  to: { x: number; y: number };
  color: string;
  type: 'straight' | 'merge' | 'branch';
}
