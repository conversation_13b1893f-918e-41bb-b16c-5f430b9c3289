import express, { Request, Response, NextFunction } from 'express';
import cors from 'cors';
import path from 'path';
import gitRoutes from './routes/git';

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Routes
app.use('/api/git', gitRoutes);

// Serve the main application
app.get('/', (req: Request, res: Response) => {
    res.sendFile(path.join(__dirname, '..', 'public', 'index.html'));
});

// Error handling middleware
app.use((err: Error, req: Request, res: Response, next: NextFunction) => {
    console.error(err.stack);
    res.status(500).json({ error: 'Something went wrong!' });
});

// 404 handler
app.use((req: Request, res: Response) => {
    res.status(404).json({ error: 'Route not found' });
});

app.listen(PORT, () => {
    console.log(`🚀 GitViewer server running on http://localhost:${PORT}`);
    console.log(`📁 Serving repository from: ${process.cwd()}`);
});

export default app;
