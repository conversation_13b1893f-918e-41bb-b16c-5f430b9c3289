import { Router, Request, Response } from 'express';
import { GitService } from '../services/gitService';
import { ApiResponse } from '../types/git';

const router = Router();

// Initialize Git service
const gitService = new GitService();

// Get repository information
router.get('/info', async (req: Request, res: Response) => {
  try {
    const isValid = await gitService.isValidRepository();
    if (!isValid) {
      return res.status(400).json({
        success: false,
        error: 'Not a valid Git repository'
      } as ApiResponse<null>);
    }

    const info = await gitService.getRepoInfo();
    res.json({
      success: true,
      data: info
    } as ApiResponse<typeof info>);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: (error as Error).message
    } as ApiResponse<null>);
  }
});

// Get commits
router.get('/commits', async (req: Request, res: Response) => {
  try {
    const limit = parseInt(req.query.limit as string) || 50;
    const commits = await gitService.getCommits(limit);
    
    res.json({
      success: true,
      data: commits
    } as ApiResponse<typeof commits>);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: (error as Error).message
    } as ApiResponse<null>);
  }
});

// Get commit graph
router.get('/commits/graph', async (req: Request, res: Response) => {
  try {
    const limit = parseInt(req.query.limit as string) || 50;
    const commits = await gitService.getCommitGraph(limit);
    
    res.json({
      success: true,
      data: commits
    } as ApiResponse<typeof commits>);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: (error as Error).message
    } as ApiResponse<null>);
  }
});

// Get commit details
router.get('/commits/:hash', async (req: Request, res: Response) => {
  try {
    const { hash } = req.params;
    const details = await gitService.getCommitDetails(hash);
    
    res.json({
      success: true,
      data: details
    } as ApiResponse<typeof details>);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: (error as Error).message
    } as ApiResponse<null>);
  }
});

// Get branches
router.get('/branches', async (req: Request, res: Response) => {
  try {
    const branches = await gitService.getBranches();
    
    res.json({
      success: true,
      data: branches
    } as ApiResponse<typeof branches>);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: (error as Error).message
    } as ApiResponse<null>);
  }
});

export default router;
