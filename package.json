{"name": "gitviewer", "version": "1.0.0", "description": "A visual Git repository viewer built with TypeScript - similar to GitKraken", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "nodemon --exec ts-node src/server.ts", "dev:frontend": "tsc --watch --project tsconfig.frontend.json", "build:frontend": "tsc --project tsconfig.frontend.json", "clean": "rm -rf dist", "prebuild": "npm run clean", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["git", "viewer", "visualization", "commits", "branches", "typescript"], "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"express": "^4.18.2", "simple-git": "^3.19.1", "cors": "^2.8.5"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/node": "^20.4.5", "typescript": "^5.1.6", "ts-node": "^10.9.1", "nodemon": "^3.0.1"}}